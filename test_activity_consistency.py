#!/usr/bin/env python3
"""
测试活动状态一致性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.persona_manager import PersonaManager
import time

def test_activity_consistency():
    """测试活动状态在同一小时内的一致性"""
    print("=== 测试活动状态一致性 ===\n")
    
    persona = PersonaManager()
    
    # 测试1: 连续多次调用，应该返回相同的活动状态
    print("测试1: 连续调用一致性")
    activities = []
    for i in range(5):
        activity = persona.get_current_activity()
        activities.append(activity)
        print(f"调用 {i+1}: {activity['activity']} | 心情: {activity['mood']}")
        time.sleep(0.1)  # 短暂延迟
    
    # 检查是否所有活动状态都相同
    first_activity = activities[0]
    all_same = all(
        act['activity'] == first_activity['activity'] and 
        act['mood'] == first_activity['mood'] 
        for act in activities
    )
    
    if all_same:
        print("✅ 测试1通过: 同一小时内活动状态保持一致")
    else:
        print("❌ 测试1失败: 活动状态不一致")
    
    print("\n" + "-" * 50 + "\n")
    
    # 测试2: 显示当前活动状态的详细信息
    print("测试2: 当前活动状态详情")
    current_activity = persona.get_current_activity()
    print(f"活动类型: {current_activity['type']}")
    print(f"具体活动: {current_activity['activity']}")
    print(f"当前心情: {current_activity['mood']}")
    print(f"时间段: {current_activity['time_period']}")
    
    print("\n" + "-" * 50 + "\n")
    
    # 测试3: 测试缓存机制
    print("测试3: 缓存机制验证")
    print(f"缓存小时: {persona._last_cache_hour}")
    print(f"缓存内容: {persona._activity_cache}")
    
    # 强制更新缓存（模拟小时变化）
    from datetime import datetime
    current_hour = datetime.now().hour
    next_hour = (current_hour + 1) % 24
    persona._update_activity_cache(next_hour)
    
    new_activity = persona.get_current_activity()
    print(f"模拟下一小时活动: {new_activity['activity']} | 心情: {new_activity['mood']}")
    
    return all_same

def test_time_based_activities():
    """测试基于时间的活动类型"""
    print("=== 测试时间段活动类型 ===\n")
    
    persona = PersonaManager()
    
    # 测试不同时间段的活动类型
    test_hours = [8, 10, 14, 19, 23]  # 早晨、上午、下午、晚上、深夜
    
    for hour in test_hours:
        persona._update_activity_cache(hour)
        activity = persona._activity_cache
        
        expected_type = "work" if 9 <= hour < 17 else "rest"
        actual_type = activity['type']
        
        print(f"时间 {hour:02d}:00 - 期望: {expected_type}, 实际: {actual_type}")
        
        if expected_type == actual_type:
            print(f"  ✅ 活动: {activity['activity']}")
        else:
            print(f"  ❌ 活动类型不匹配")
        
        print(f"  心情: {activity['mood']}")
        print(f"  时间段: {activity['time_period']}")
        print()

if __name__ == "__main__":
    success1 = test_activity_consistency()
    test_time_based_activities()
    
    if success1:
        print("🎉 所有测试通过！活动状态一致性修正成功。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
