#!/usr/bin/env python3
"""
提示词管理工具
用于管理和编辑Jinja2模板
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.prompt_manager import PromptManager, PromptTemplateManager

class PromptManagementTool:
    """提示词管理工具"""
    
    def __init__(self):
        self.prompt_manager = PromptManager()
        self.template_manager = PromptTemplateManager(self.prompt_manager)
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("📝 虚拟人小雨 - 提示词管理工具")
        print("="*50)
        print("1. 查看所有模板")
        print("2. 查看模板内容")
        print("3. 编辑模板")
        print("4. 创建新模板")
        print("5. 删除模板")
        print("6. 测试模板渲染")
        print("7. 预览完整系统提示词")
        print("0. 退出")
        print("="*50)
    
    def list_templates(self):
        """列出所有模板"""
        templates = self.template_manager.list_templates()
        print(f"\n📚 所有模板文件 (共 {len(templates)} 个):")
        for i, template in enumerate(templates, 1):
            print(f"  {i}. {template}")
    
    def view_template(self):
        """查看模板内容"""
        templates = self.template_manager.list_templates()
        if not templates:
            print("❌ 没有找到模板文件")
            return
        
        self.list_templates()
        try:
            choice = int(input("\n请选择要查看的模板编号: ")) - 1
            if 0 <= choice < len(templates):
                template_name = templates[choice]
                content = self.template_manager.get_template_content(template_name)
                
                print(f"\n📄 模板内容: {template_name}")
                print("-" * 50)
                print(content)
                print("-" * 50)
                print(f"内容长度: {len(content)} 字符")
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def edit_template(self):
        """编辑模板"""
        templates = self.template_manager.list_templates()
        if not templates:
            print("❌ 没有找到模板文件")
            return
        
        self.list_templates()
        try:
            choice = int(input("\n请选择要编辑的模板编号: ")) - 1
            if 0 <= choice < len(templates):
                template_name = templates[choice]
                current_content = self.template_manager.get_template_content(template_name)
                
                print(f"\n✏️  编辑模板: {template_name}")
                print("当前内容:")
                print("-" * 30)
                print(current_content)
                print("-" * 30)
                
                print("\n请输入新内容 (输入 'END' 结束):")
                new_lines = []
                while True:
                    line = input()
                    if line.strip() == 'END':
                        break
                    new_lines.append(line)
                
                new_content = '\n'.join(new_lines)
                
                # 验证模板语法
                is_valid, message = self.template_manager.validate_template(new_content)
                if is_valid:
                    self.template_manager.save_template(template_name, new_content)
                    print("✅ 模板保存成功")
                else:
                    print(f"❌ 模板语法错误: {message}")
                    save_anyway = input("是否仍要保存? (y/N): ").lower() == 'y'
                    if save_anyway:
                        self.template_manager.save_template(template_name, new_content)
                        print("⚠️  模板已保存，但可能存在语法错误")
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def create_template(self):
        """创建新模板"""
        template_name = input("\n📝 请输入新模板名称 (不需要.j2后缀): ").strip()
        if not template_name:
            print("❌ 模板名称不能为空")
            return
        
        if not template_name.endswith('.j2'):
            template_name += '.j2'
        
        print(f"创建模板: {template_name}")
        print("请输入模板内容 (输入 'END' 结束):")
        
        lines = []
        while True:
            line = input()
            if line.strip() == 'END':
                break
            lines.append(line)
        
        content = '\n'.join(lines)
        
        # 验证模板语法
        is_valid, message = self.template_manager.validate_template(content)
        if is_valid:
            self.template_manager.create_template(template_name, content)
            print("✅ 模板创建成功")
        else:
            print(f"❌ 模板语法错误: {message}")
            save_anyway = input("是否仍要保存? (y/N): ").lower() == 'y'
            if save_anyway:
                self.template_manager.create_template(template_name, content)
                print("⚠️  模板已创建，但可能存在语法错误")
    
    def delete_template(self):
        """删除模板"""
        templates = self.template_manager.list_templates()
        if not templates:
            print("❌ 没有找到模板文件")
            return
        
        self.list_templates()
        try:
            choice = int(input("\n请选择要删除的模板编号: ")) - 1
            if 0 <= choice < len(templates):
                template_name = templates[choice]
                
                confirm = input(f"确认删除模板 '{template_name}'? (y/N): ").lower() == 'y'
                if confirm:
                    self.template_manager.delete_template(template_name)
                    print("✅ 模板删除成功")
                else:
                    print("❌ 取消删除")
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def test_template_rendering(self):
        """测试模板渲染"""
        templates = self.template_manager.list_templates()
        if not templates:
            print("❌ 没有找到模板文件")
            return
        
        self.list_templates()
        try:
            choice = int(input("\n请选择要测试的模板编号: ")) - 1
            if 0 <= choice < len(templates):
                template_name = templates[choice]
                
                # 使用示例数据测试
                test_vars = self._get_test_variables()
                
                try:
                    result = self.prompt_manager.render_template(template_name, **test_vars)
                    print(f"\n🧪 模板渲染结果: {template_name}")
                    print("-" * 50)
                    print(result)
                    print("-" * 50)
                    print(f"渲染结果长度: {len(result)} 字符")
                except Exception as e:
                    print(f"❌ 渲染失败: {e}")
            else:
                print("❌ 无效的选择")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def preview_system_prompt(self):
        """预览完整系统提示词"""
        test_vars = self._get_test_variables()
        
        try:
            system_prompt = self.prompt_manager.render_system_prompt(**test_vars)
            print("\n🔍 完整系统提示词预览:")
            print("=" * 60)
            print(system_prompt)
            print("=" * 60)
            print(f"总长度: {len(system_prompt)} 字符")
        except Exception as e:
            print(f"❌ 渲染失败: {e}")
    
    def _get_test_variables(self):
        """获取测试用的模板变量"""
        from config import Config
        
        return {
            'persona': {
                'name': '小雨',
                'age': 25,
                'profession': '心理咨询师',
                'personality': '温和、善解人意',
                'hobbies': '画画、听音乐',
                'speaking_style': '温柔、自然',
            },
            'current_time': '2025年5月29日 22:30',
            'current_activity': '在家听音乐',
            'activity_description': '正在放松，听着轻柔的音乐',
            'time_context': {
                'period': '晚上',
                'greeting': '晚上好',
                'mood': '放松'
            },
            'emotion_analysis': {
                'emotion': 'happy',
                'confidence': 0.8,
                'keywords': ['开心', '愉快']
            },
            'affection_level': 60,
            'memories': [
                {'content': '用户喜欢画画'},
                {'content': '用户最近在学习新技能'}
            ],
            'persona_memories': [
                {
                    'title': '第一次画画',
                    'content': '5岁时妈妈给我买了第一盒水彩笔',
                    'emotion': 'happy'
                }
            ],
            'speaking_examples': Config.PERSONA_CONFIG['speaking_examples']
        }
    
    def run(self):
        """运行工具"""
        while True:
            self.show_menu()
            choice = input("\n请选择操作 (0-7): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                self.list_templates()
            elif choice == '2':
                self.view_template()
            elif choice == '3':
                self.edit_template()
            elif choice == '4':
                self.create_template()
            elif choice == '5':
                self.delete_template()
            elif choice == '6':
                self.test_template_rendering()
            elif choice == '7':
                self.preview_system_prompt()
            else:
                print("❌ 无效选择，请重试")
            
            input("\n按回车键继续...")

if __name__ == "__main__":
    tool = PromptManagementTool()
    tool.run()
