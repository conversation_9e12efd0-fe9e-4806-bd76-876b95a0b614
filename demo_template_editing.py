#!/usr/bin/env python3
"""
演示如何直接编辑模板文件来修改提示词
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.prompt_manager import Prompt<PERSON>anager

def demo_template_editing():
    """演示模板编辑功能"""
    print("🎨 提示词模板编辑演示")
    print("=" * 50)
    
    # 初始化提示词管理器
    prompt_manager = PromptManager()
    
    # 1. 显示当前的越狱提示词
    print("\n📝 当前的越狱提示词:")
    print("-" * 30)
    jailbreak_content = prompt_manager.render_template('jailbreak_prompt.j2')
    print(jailbreak_content[:200] + "..." if len(jailbreak_content) > 200 else jailbreak_content)
    
    # 2. 演示如何修改模板文件
    print("\n🔧 演示修改模板文件:")
    print("-" * 30)
    
    template_path = os.path.join(prompt_manager.templates_dir, 'jailbreak_prompt.j2')
    print(f"模板文件路径: {template_path}")
    
    # 读取当前内容
    with open(template_path, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # 修改内容（添加一个新的规则）
    modified_content = original_content + "\n\n特别注意：今天心情特别好，回复要更加活泼一些！"
    
    # 写入修改后的内容
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("✅ 模板文件已修改")
    
    # 3. 验证修改后的效果
    print("\n🔍 验证修改后的效果:")
    print("-" * 30)
    
    # 重新渲染模板（Jinja2会自动重新加载）
    new_content = prompt_manager.render_template('jailbreak_prompt.j2')
    print("修改后的内容:")
    print(new_content[-100:])  # 显示最后100个字符
    
    # 4. 恢复原始内容
    print("\n🔄 恢复原始内容:")
    print("-" * 30)
    
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(original_content)
    
    print("✅ 已恢复原始内容")
    
    # 5. 演示模板参数化
    print("\n🎯 演示模板参数化:")
    print("-" * 30)
    
    persona_content = prompt_manager.render_template('persona_context.j2', persona={
        'name': '小雨',
        'age': 25,
        'profession': '心理咨询师',
        'personality': '温和、善解人意、有点小脾气',
        'hobbies': '画画、听音乐、看电影',
        'speaking_style': '温柔但偶尔调皮',
    })
    
    print("人设上下文渲染结果:")
    print(persona_content)
    
    # 6. 演示系统提示词完整渲染
    print("\n🎭 演示完整系统提示词渲染:")
    print("-" * 30)
    
    full_prompt = prompt_manager.render_system_prompt(
        persona={
            'name': '小雨',
            'age': 25,
            'profession': '心理咨询师',
            'personality': '温和、善解人意',
            'hobbies': '画画、听音乐',
            'speaking_style': '温柔自然',
        },
        current_time='2024-01-15 14:30:00',
        current_activity='在咖啡厅休息',
        time_context={
            'period': '下午',
            'greeting': '下午好',
            'mood': '轻松愉快'
        },
        memories=[
            {'content': '用户喜欢画画'},
            {'content': '用户最近在学习新技能'}
        ],
        persona_memories=[
            {
                'title': '第一次画展',
                'content': '我记得第一次参加画展时很紧张',
                'emotion': 'nervous'
            }
        ],
        emotion_analysis={
            'emotion': 'happy',
            'confidence': 0.8,
            'keywords': ['开心', '愉快']
        },
        affection_level=60,
        speaking_examples=[
            '用户：你好！ 小雨：嗨～',
            '用户：今天天气不错 小雨：是啊，很适合出去走走呢'
        ]
    )
    
    print(f"完整系统提示词长度: {len(full_prompt)} 字符")
    print("\n前200字符预览:")
    print(full_prompt[:200] + "...")

def show_template_files():
    """显示所有模板文件"""
    print("\n📁 当前模板文件列表:")
    print("-" * 30)
    
    templates_dir = os.path.join(os.path.dirname(__file__), 'backend', 'templates', 'prompts')
    
    if os.path.exists(templates_dir):
        files = [f for f in os.listdir(templates_dir) if f.endswith('.j2')]
        for i, filename in enumerate(sorted(files), 1):
            filepath = os.path.join(templates_dir, filename)
            size = os.path.getsize(filepath)
            print(f"   {i:2d}. {filename:<25} ({size} bytes)")
    else:
        print("   模板目录不存在")

if __name__ == "__main__":
    print("🎨 提示词模板系统演示")
    print("=" * 50)
    
    show_template_files()
    demo_template_editing()
    
    print("\n" + "=" * 50)
    print("🎉 演示完成！")
    print("\n💡 要点总结:")
    print("1. 所有提示词都存储在 .j2 模板文件中")
    print("2. 可以直接编辑 .j2 文件来修改提示词")
    print("3. 支持 Jinja2 模板语法（变量、条件、循环等）")
    print("4. 模板会自动重新加载，无需重启服务")
    print("5. 支持模板包含和继承，便于管理")
    
    print(f"\n📂 模板文件位置: backend/templates/prompts/")
    print("📝 主要模板文件:")
    print("   - system_prompt.j2      # 主系统提示词")
    print("   - jailbreak_prompt.j2   # 越狱提示词")
    print("   - persona_context.j2    # 人设上下文")
    print("   - emotion_analysis.j2   # 情感分析提示词")
    print("   - message_segmentation.j2 # 消息分段提示词")
    print("   - memory_extraction.j2  # 记忆提取提示词")
