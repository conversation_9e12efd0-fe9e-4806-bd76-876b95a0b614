#!/usr/bin/env python3
"""
调试标点符号处理
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_punctuation_cleaning():
    """测试标点符号清理功能"""
    from services.conversation_engine import ConversationEngine
    
    engine = ConversationEngine()
    
    test_cases = [
        "测试多个标点符号！！！这应该保留情绪。。。",
        "好吧，知道了。",
        "太棒了！",
        "哈哈哈！！！",
        "什么。。。",
        "真的吗？？？",
    ]
    
    print("=== 标点符号处理调试 ===\n")
    
    for i, text in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {text}")
        
        # 测试备用分段
        fallback_segments = engine._fallback_split(text)
        print(f"备用分段结果: {fallback_segments}")
        
        # 测试标点符号清理
        cleaned = engine._clean_segment_punctuation([text])
        print(f"直接清理结果: {cleaned}")
        
        # 测试完整流程
        final_segments = engine._split_response_into_segments(text)
        print(f"完整流程结果: {final_segments}")
        
        print("-" * 50)

def test_regex_patterns():
    """测试正则表达式模式"""
    import re
    
    test_strings = [
        "测试！！！",
        "测试。。。", 
        "测试？？？",
        "测试！",
        "测试。",
        "测试？",
        "测试！？",
        "测试？！",
    ]
    
    print("\n=== 正则表达式测试 ===\n")
    
    pattern = r'([！!]{2,}|[？?]{2,}|[。.]{2,})$'
    
    for text in test_strings:
        match = re.search(pattern, text)
        print(f"'{text}' -> 匹配: {bool(match)}")
        if match:
            print(f"  匹配内容: '{match.group()}'")

if __name__ == "__main__":
    test_regex_patterns()
    test_punctuation_cleaning()
