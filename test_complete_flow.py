#!/usr/bin/env python3
"""
测试完整的聊天流程，包括分段功能
"""

import requests
import json
import time

def test_chat_api():
    """测试聊天API的分段功能"""
    base_url = "http://localhost:8080"
    
    # 测试用例：不同长度的消息
    test_messages = [
        "你好",
        "今天天气怎么样？",
        "我今天去了一家很棒的咖啡店，环境特别好，咖啡也很香。然后我还买了一本关于心理学的书，你有什么推荐的书吗？",
        "我最近在学习画画，感觉很有趣但也很有挑战性。你觉得我应该从哪个方面开始练习比较好？另外，你平时都喜欢做什么来放松自己呢？"
    ]
    
    user_id = "test_user_" + str(int(time.time()))
    
    print("=== 测试聊天API分段功能 ===\n")
    
    for i, message in enumerate(test_messages, 1):
        print(f"测试用例 {i}:")
        print(f"发送消息: {message}")
        
        try:
            response = requests.post(
                f"{base_url}/api/chat",
                json={
                    "user_id": user_id,
                    "message": message
                },
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"完整回复: {data['response']}")
                    
                    segments = data.get('response_segments', [])
                    if len(segments) > 1:
                        print(f"分段回复 ({len(segments)} 段):")
                        for j, segment in enumerate(segments, 1):
                            print(f"  {j}. {segment}")
                    else:
                        print("单段回复（未分段）")
                    
                    print(f"好感度: {data.get('affection_level', 'N/A')}")
                    print(f"好感度变化: {data.get('affection_change', 'N/A')}")
                else:
                    print(f"API错误: {data.get('error', '未知错误')}")
            else:
                print(f"HTTP错误: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
        
        print("-" * 60)
        time.sleep(1)  # 避免请求过快

if __name__ == "__main__":
    test_chat_api()
