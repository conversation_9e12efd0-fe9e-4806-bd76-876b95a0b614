#!/usr/bin/env python3
"""
系统测试脚本 - 验证各个模块是否正常工作
"""

import os
import sys
import json
from datetime import datetime

# 添加backend目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_database():
    """测试数据库功能"""
    print("🔍 测试数据库功能...")
    try:
        from models.database import DatabaseManager

        db = DatabaseManager()

        # 测试用户创建
        user = db.get_user_or_create("test_user", "测试用户")
        print(f"✅ 用户创建成功: {user['name']}")

        # 测试对话保存
        db.save_conversation("test_user", "你好", "user")
        db.save_conversation("test_user", "你好！很高兴见到你", "assistant")
        print("✅ 对话保存成功")

        # 测试对话获取
        conversations = db.get_recent_conversations("test_user", 2)
        print(f"✅ 对话获取成功: {len(conversations)} 条记录")

        # 测试记忆保存
        db.save_memory("test_user", "personal", "喜欢看电影", 2.0)
        print("✅ 记忆保存成功")

        # 测试记忆获取
        memories = db.get_relevant_memories("test_user", ["电影"], 5)
        print(f"✅ 记忆获取成功: {len(memories)} 条记录")

        # 测试好感度
        current_affection = db.get_current_affection("test_user")
        new_affection = db.update_affection("test_user", 5, "测试增加")
        print(f"✅ 好感度测试成功: {current_affection} -> {new_affection}")

        return True

    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_persona_manager():
    """测试人设管理器"""
    print("\n🎭 测试人设管理器...")
    try:
        from services.persona_manager import PersonaManager

        persona = PersonaManager()

        # 测试问候语
        greeting = persona.get_time_based_greeting()
        print(f"✅ 问候语生成成功: {greeting[:30]}...")

        # 测试当前活动
        activity = persona.get_current_activity()
        print(f"✅ 活动状态生成成功: {activity['activity'][:30]}...")

        # 测试人设上下文
        context = persona.get_persona_context(50)
        print(f"✅ 人设上下文生成成功: {len(context)} 字符")

        return True

    except Exception as e:
        print(f"❌ 人设管理器测试失败: {e}")
        return False

def test_conversation_engine():
    """测试对话引擎（不包含LLM调用）"""
    print("\n💬 测试对话引擎...")
    try:
        from services.conversation_engine import ConversationEngine

        engine = ConversationEngine()

        # 测试问候消息
        greeting_result = engine.get_greeting_message("test_user_2")
        print(f"✅ 问候消息生成成功: {greeting_result['response'][:30]}...")
        print(f"   好感度: {greeting_result['affection_level']}")

        return True

    except Exception as e:
        print(f"❌ 对话引擎测试失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n⚙️  测试配置文件...")
    try:
        from config import Config

        print(f"✅ 虚拟人姓名: {Config.PERSONA_CONFIG['name']}")
        print(f"✅ 初始好感度: {Config.AFFECTION_CONFIG['initial_level']}")
        print(f"✅ 数据库路径: {Config.DATABASE_PATH}")

        return True

    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_env_file():
    """测试环境变量文件"""
    print("\n🔐 测试环境变量...")
    env_path = os.path.join("backend", ".env")

    if os.path.exists(env_path):
        print("✅ .env 文件存在")

        # 检查是否包含必要的配置项
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()

        required_keys = ['VOLCENGINE_ACCESS_KEY', 'VOLCENGINE_SECRET_KEY']
        missing_keys = []

        for key in required_keys:
            if key not in content:
                missing_keys.append(key)

        if missing_keys:
            print(f"⚠️  缺少配置项: {', '.join(missing_keys)}")
            print("   请在 backend/.env 文件中添加火山引擎API配置")
        else:
            print("✅ 环境变量配置完整")

        return True
    else:
        print("⚠️  .env 文件不存在，请运行 start.py 自动创建")
        return False

def main():
    """主测试函数"""
    print("🧪 虚拟人恋爱陪伴系统 - 功能测试")
    print("=" * 50)

    tests = [
        ("配置文件", test_config),
        ("环境变量", test_env_file),
        ("数据库", test_database),
        ("人设管理器", test_persona_manager),
        ("对话引擎", test_conversation_engine),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！系统准备就绪")
        print("\n📝 下一步:")
        print("1. 配置 backend/.env 文件中的火山引擎API")
        print("2. 运行 python start.py 启动系统")
        print("3. 访问 http://localhost:5000 开始使用")
    else:
        print("⚠️  部分测试失败，请检查错误信息")

    return passed == total

if __name__ == "__main__":
    main()
