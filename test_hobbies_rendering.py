#!/usr/bin/env python3
"""
测试爱好信息渲染
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.conversation_engine import ConversationEngine

def test_hobbies_rendering():
    """测试爱好信息渲染"""
    print("🎨 测试爱好信息渲染")
    print("=" * 50)
    
    try:
        # 创建对话引擎
        engine = ConversationEngine()
        
        # 测试数据
        test_memories = [
            {'content': '用户喜欢画画'},
            {'content': '用户最近在学习新技能'}
        ]
        
        test_persona_memories = [
            {
                'title': '第一次画展',
                'content': '我记得第一次参加画展时很紧张',
                'emotion': 'nervous'
            }
        ]
        
        test_emotion_analysis = {
            'emotion': 'happy',
            'intensity': 0.8,
            'keywords': ['开心', '愉快']
        }
        
        # 调用 _build_system_prompt 方法
        system_prompt = engine._build_system_prompt(
            affection_level=60,
            emotion_analysis=test_emotion_analysis,
            memories=test_memories,
            persona_memories=test_persona_memories
        )
        
        print("✅ 系统提示词构建成功")
        print(f"📏 提示词长度: {len(system_prompt)} 字符")
        
        # 显示完整的提示词内容
        print("\n📝 完整提示词内容:")
        print("-" * 50)
        print(system_prompt)
        print("-" * 50)
        
        # 检查是否包含爱好信息
        if '唱歌' in system_prompt:
            print("✅ 提示词包含唱歌爱好")
        if '滑雪' in system_prompt:
            print("✅ 提示词包含滑雪爱好")
        if '画画' in system_prompt:
            print("✅ 提示词包含画画爱好")
        if '周杰伦' in system_prompt:
            print("✅ 提示词包含喜欢的歌手信息")
        if '演唱会' in system_prompt:
            print("✅ 提示词包含演唱会经历")
        if '旅游' in system_prompt:
            print("✅ 提示词包含旅游信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_hobbies_rendering()
