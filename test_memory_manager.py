#!/usr/bin/env python3
"""
测试记忆管理模块
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.memory_manager import MemoryManager
from services.mock_llm_service import MockLLMService
from models.database import DatabaseManager

def test_memory_manager():
    """测试记忆管理器功能"""
    print("=== 测试记忆管理模块 ===\n")
    
    # 初始化组件
    db = DatabaseManager()
    llm = MockLLMService()
    memory_manager = MemoryManager(llm_service=llm, db_manager=db)
    
    test_user_id = "test_memory_user"
    
    # 测试用例
    test_messages = [
        "我叫张三，今年25岁，是一名程序员",
        "我很喜欢看电影，特别是科幻片和悬疑片",
        "昨天我去了一家新开的咖啡店，环境很不错",
        "我计划明年去日本旅行，想看樱花",
        "最近工作压力有点大，感觉有些焦虑",
        "我在学习画画，希望能提高自己的艺术修养"
    ]
    
    print("1. 测试记忆提取和保存:")
    for i, message in enumerate(test_messages, 1):
        print(f"\n测试消息 {i}: {message}")
        memories = memory_manager.extract_memories_from_text(test_user_id, message)
        print(f"提取记忆数量: {len(memories)}")
        for j, memory in enumerate(memories, 1):
            print(f"  {j}. [{memory.get('type')}] {memory.get('content')} (重要性: {memory.get('importance')})")
    
    print("\n" + "="*60)
    print("2. 测试关键词提取:")
    test_keywords_text = "我想学习画画和音乐，还想去旅行看看世界"
    keywords = memory_manager.extract_keywords(test_keywords_text)
    print(f"文本: {test_keywords_text}")
    print(f"提取关键词: {keywords}")
    
    print("\n" + "="*60)
    print("3. 测试记忆检索:")
    search_keywords = ["画画", "艺术"]
    relevant_memories = memory_manager.get_relevant_memories(test_user_id, keywords=search_keywords, limit=5)
    print(f"搜索关键词: {search_keywords}")
    print(f"相关记忆数量: {len(relevant_memories)}")
    for i, memory in enumerate(relevant_memories, 1):
        print(f"  {i}. [{memory.get('type')}] {memory.get('content')}")
    
    print("\n" + "="*60)
    print("4. 测试记忆摘要:")
    summary = memory_manager.get_memory_summary(test_user_id)
    print(f"记忆摘要:")
    print(f"  总记忆数: {summary.get('total_memories')}")
    print(f"  平均重要性: {summary.get('average_importance'):.2f}")
    print(f"  最常见类型: {summary.get('most_common_type')}")
    print(f"  类型分布: {summary.get('type_distribution')}")
    
    print("\n" + "="*60)
    print("5. 测试无LLM的规则提取:")
    memory_manager_no_llm = MemoryManager(llm_service=None, db_manager=db)
    test_text = "我喜欢听音乐，昨天买了一张新专辑，计划周末去听演唱会"
    memories_rule_based = memory_manager_no_llm.extract_memories_from_text(test_user_id + "_rule", test_text)
    print(f"规则提取结果: {len(memories_rule_based)} 条记忆")
    for i, memory in enumerate(memories_rule_based, 1):
        print(f"  {i}. [{memory.get('type')}] {memory.get('content')}")

def test_memory_types():
    """测试记忆类型验证"""
    print("\n" + "="*60)
    print("6. 测试记忆类型验证:")
    
    memory_manager = MemoryManager()
    
    # 有效记忆
    valid_memory = {
        "type": "personal",
        "content": "用户是程序员",
        "importance": "2.0"
    }
    
    # 无效记忆
    invalid_memories = [
        {"type": "invalid_type", "content": "test", "importance": "2.0"},  # 无效类型
        {"content": "test", "importance": "2.0"},  # 缺少类型
        {"type": "personal", "importance": "2.0"},  # 缺少内容
        {"type": "personal", "content": "test", "importance": "5.0"},  # 重要性超出范围
    ]
    
    print(f"有效记忆验证: {memory_manager._is_valid_memory(valid_memory)}")
    
    for i, invalid_memory in enumerate(invalid_memories, 1):
        result = memory_manager._is_valid_memory(invalid_memory)
        print(f"无效记忆 {i} 验证: {result} - {invalid_memory}")

if __name__ == "__main__":
    test_memory_manager()
    test_memory_types()
    print("\n✅ 记忆管理模块测试完成！")
