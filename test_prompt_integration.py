#!/usr/bin/env python3
"""
测试提示词模块集成
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.conversation_engine import ConversationEngine
from services.prompt_manager import PromptManager

def test_prompt_integration():
    """测试提示词模块集成"""
    print("🧪 测试提示词模块集成...")

    try:
        # 创建对话引擎
        engine = ConversationEngine()

        # 测试提示词管理器是否正确初始化
        assert hasattr(engine, 'prompt_manager'), "❌ 对话引擎缺少 prompt_manager 属性"
        assert isinstance(engine.prompt_manager, PromptManager), "❌ prompt_manager 类型错误"
        print("✅ 提示词管理器初始化成功")

        # 测试系统提示词构建
        test_memories = [
            {'content': '用户喜欢画画'},
            {'content': '用户最近在学习新技能'}
        ]

        test_persona_memories = [
            {
                'title': '第一次画展',
                'content': '我记得第一次参加画展时很紧张',
                'emotion': 'nervous'
            }
        ]

        test_emotion_analysis = {
            'emotion': 'happy',
            'intensity': 0.8,
            'keywords': ['开心', '愉快']
        }

        # 调用 _build_system_prompt 方法
        system_prompt = engine._build_system_prompt(
            affection_level=60,
            emotion_analysis=test_emotion_analysis,
            memories=test_memories,
            persona_memories=test_persona_memories
        )

        print("✅ 系统提示词构建成功")
        print(f"📏 提示词长度: {len(system_prompt)} 字符")

        # 验证提示词内容包含关键元素
        assert '乔青青' in system_prompt, "❌ 提示词缺少人设名称"
        assert '用户喜欢画画' in system_prompt, "❌ 提示词缺少用户记忆"
        assert '第一次画展' in system_prompt, "❌ 提示词缺少个人记忆"
        assert 'happy' in system_prompt or '开心' in system_prompt, "❌ 提示词缺少情感信息"
        assert '周杰伦' in system_prompt, "❌ 提示词缺少详细爱好信息"
        assert '演唱会' in system_prompt, "❌ 提示词缺少演唱会经历"

        print("✅ 提示词内容验证通过")

        # 显示提示词片段
        print("\n📝 提示词片段预览:")
        lines = system_prompt.split('\n')
        for i, line in enumerate(lines[:10]):  # 显示前10行
            print(f"   {i+1:2d}: {line}")
        if len(lines) > 10:
            print(f"   ... (还有 {len(lines)-10} 行)")

        return True

    except Exception as e:
        print(f"❌ 提示词模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_rendering():
    """测试模板渲染功能"""
    print("\n🎨 测试模板渲染功能...")

    try:
        prompt_manager = PromptManager()

        # 测试单个模板渲染
        jailbreak_content = prompt_manager.render_template('jailbreak_prompt.j2')
        print("✅ 越狱提示词模板渲染成功")

        # 测试带参数的模板渲染
        persona_content = prompt_manager.render_template('persona_context.j2', persona={
            'name': '小雨',
            'age': 25,
            'profession': '心理咨询师',
            'personality': '温和、善解人意',
            'hobbies': '画画、听音乐',
            'speaking_style': '温柔、自然',
        })
        print("✅ 人设上下文模板渲染成功")

        # 验证渲染结果
        assert '小雨' in persona_content, "❌ 人设模板渲染结果缺少姓名"
        assert '25' in persona_content, "❌ 人设模板渲染结果缺少年龄"

        print("✅ 模板渲染验证通过")

        return True

    except Exception as e:
        print(f"❌ 模板渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 提示词模块集成测试")
    print("=" * 50)

    tests = [
        test_prompt_integration,
        test_template_rendering
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！提示词模块集成成功")
    else:
        print("❌ 部分测试失败，请检查问题")
        sys.exit(1)
