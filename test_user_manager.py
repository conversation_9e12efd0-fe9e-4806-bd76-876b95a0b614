#!/usr/bin/env python3
"""
测试用户管理模块
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.user_manager import UserManager
from models.database import DatabaseManager

def test_user_management():
    """测试用户管理功能"""
    print("=== 测试用户管理模块 ===\n")
    
    # 初始化组件
    db = DatabaseManager()
    user_manager = UserManager(db)
    
    print("1. 测试用户注册:")
    try:
        # 测试用户注册
        user1 = user_manager.create_user(
            username="testuser1",
            password="password123",
            nickname="测试用户1",
            email="<EMAIL>"
        )
        print(f"✅ 用户注册成功: {user1}")
        
        # 测试无密码用户注册
        user2 = user_manager.create_user(
            username="guestuser",
            nickname="游客用户"
        )
        print(f"✅ 游客用户注册成功: {user2}")
        
    except Exception as e:
        print(f"❌ 用户注册失败: {e}")
    
    print("\n" + "="*60)
    print("2. 测试用户认证:")
    try:
        # 测试正确密码登录
        auth_user = user_manager.authenticate_user("testuser1", "password123")
        if auth_user:
            print(f"✅ 用户认证成功: {auth_user['username']}")
        else:
            print("❌ 用户认证失败")
        
        # 测试错误密码
        auth_fail = user_manager.authenticate_user("testuser1", "wrongpassword")
        if not auth_fail:
            print("✅ 错误密码正确被拒绝")
        else:
            print("❌ 错误密码被接受")
            
    except Exception as e:
        print(f"❌ 用户认证测试失败: {e}")
    
    print("\n" + "="*60)
    print("3. 测试会话管理:")
    try:
        if auth_user:
            # 创建会话
            session_token = user_manager.create_session(auth_user['user_id'])
            print(f"✅ 会话创建成功: {session_token[:16]}...")
            
            # 验证会话
            session_user = user_manager.validate_session(session_token)
            if session_user:
                print(f"✅ 会话验证成功: {session_user['username']}")
            else:
                print("❌ 会话验证失败")
            
            # 登出
            logout_success = user_manager.logout_user(session_token)
            if logout_success:
                print("✅ 用户登出成功")
            else:
                print("❌ 用户登出失败")
            
            # 验证登出后的会话
            session_user_after_logout = user_manager.validate_session(session_token)
            if not session_user_after_logout:
                print("✅ 登出后会话正确失效")
            else:
                print("❌ 登出后会话仍然有效")
                
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")
    
    print("\n" + "="*60)
    print("4. 测试用户信息管理:")
    try:
        if auth_user:
            # 获取用户信息
            user_info = user_manager.get_user_info(auth_user['user_id'])
            if user_info:
                print(f"✅ 获取用户信息成功: {user_info['nickname']}")
            
            # 更新用户信息
            update_success = user_manager.update_user_info(
                auth_user['user_id'],
                nickname="更新后的昵称",
                email="<EMAIL>"
            )
            if update_success:
                print("✅ 用户信息更新成功")
                
                # 验证更新
                updated_info = user_manager.get_user_info(auth_user['user_id'])
                if updated_info and updated_info['nickname'] == "更新后的昵称":
                    print("✅ 用户信息更新验证成功")
                else:
                    print("❌ 用户信息更新验证失败")
            else:
                print("❌ 用户信息更新失败")
                
    except Exception as e:
        print(f"❌ 用户信息管理测试失败: {e}")
    
    print("\n" + "="*60)
    print("5. 测试用户列表:")
    try:
        # 获取用户列表
        user_list = user_manager.list_users(page=1, page_size=10)
        print(f"✅ 获取用户列表成功: {user_list['pagination']['total']} 个用户")
        
        for user in user_list['users']:
            print(f"   - {user['username']} ({user['nickname']}) - {user['status']}")
            
    except Exception as e:
        print(f"❌ 用户列表测试失败: {e}")
    
    print("\n" + "="*60)
    print("6. 测试输入验证:")
    
    # 测试无效用户名
    try:
        user_manager.create_user(username="ab", password="123456")
        print("❌ 短用户名应该被拒绝")
    except ValueError:
        print("✅ 短用户名正确被拒绝")
    
    # 测试无效密码
    try:
        user_manager.create_user(username="validuser", password="123")
        print("❌ 短密码应该被拒绝")
    except ValueError:
        print("✅ 短密码正确被拒绝")
    
    # 测试无效邮箱
    try:
        user_manager.create_user(username="validuser2", email="invalid-email")
        print("❌ 无效邮箱应该被拒绝")
    except ValueError:
        print("✅ 无效邮箱正确被拒绝")
    
    # 测试重复用户名
    try:
        user_manager.create_user(username="testuser1", password="123456")
        print("❌ 重复用户名应该被拒绝")
    except ValueError:
        print("✅ 重复用户名正确被拒绝")

def test_api_integration():
    """测试API集成"""
    print("\n" + "="*60)
    print("7. API集成测试提示:")
    print("请手动测试以下API接口:")
    print("- POST /api/users/register - 用户注册")
    print("- POST /api/users/login - 用户登录")
    print("- POST /api/users/logout - 用户登出")
    print("- GET /api/users/profile - 获取用户资料")
    print("- PUT /api/users/profile - 更新用户资料")
    print("\n示例测试命令:")
    print("curl -X POST http://localhost:8080/api/users/register \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{\"username\":\"testapi\",\"password\":\"123456\",\"nickname\":\"API测试用户\"}'")

if __name__ == "__main__":
    test_user_management()
    test_api_integration()
    print("\n✅ 用户管理模块测试完成！")
