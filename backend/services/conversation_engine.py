from typing import Dict, List, Optional
from datetime import datetime
import random
import re

from models.database import DatabaseManager
from services.llm_service import VolcengineLLMService
from services.mock_llm_service import MockLLMService
from services.persona_manager import PersonaManager
from services.memory_manager import MemoryManager
from services.message_processor import MessageProcessor
from services.persona_memory_manager import PersonaMemoryManager
from services.prompt_manager import PromptManager
from config import Config
import os

class ConversationEngine:
    def __init__(self):
        self.db = DatabaseManager()

        # 检查是否配置了火山引擎API
        if self._is_volcengine_configured():
            self.llm = VolcengineLLMService()
            print("✅ 使用火山引擎LLM服务")
        else:
            self.llm = MockLLMService()
            print("⚠️  使用模拟LLM服务（请配置火山引擎API以获得更好体验）")

        self.persona = PersonaManager()
        self.memory_manager = MemoryManager(llm_service=self.llm, db_manager=self.db)
        self.persona_memory_manager = PersonaMemoryManager(db_manager=self.db)
        self.message_processor = MessageProcessor(llm_service=self.llm)
        self.prompt_manager = PromptManager()

    def _is_volcengine_configured(self) -> bool:
        """检查火山引擎API是否已配置"""
        return (Config.VOLCENGINE_ACCESS_KEY and
                Config.VOLCENGINE_ACCESS_KEY != 'your_access_key_here')

    def process_message(self, user_id: str, message: str) -> Dict:
        """
        处理用户消息并生成回复

        Args:
            user_id: 用户ID
            message: 用户消息

        Returns:
            包含回复和相关信息的字典
        """
        # 1. 获取或创建用户
        user = self.db.get_user_or_create(user_id)

        # 2. 分析用户情感
        emotion_analysis = self.llm.analyze_emotion(message)

        # 3. 更新好感度
        affection_change = self._calculate_affection_change(message, emotion_analysis)
        current_affection = self.db.update_affection(
            user_id,
            affection_change,
            f"对话互动: {message[:20]}..."
        )

        # 4. 获取对话历史
        conversation_history = self.db.get_recent_conversations(user_id, limit=8)

        # 5. 获取相关记忆
        keywords = self.memory_manager.extract_keywords(message)
        relevant_memories = self.memory_manager.get_relevant_memories(
            user_id,
            keywords=keywords,
            limit=3
        )

        # 6. 获取相关的个人记忆
        persona_memories = self.persona_memory_manager.get_relevant_persona_memories(
            message,
            user_affection=current_affection,
            limit=2
        )

        # 7. 构建系统提示词
        system_prompt = self._build_system_prompt(
            current_affection,
            emotion_analysis,
            relevant_memories,
            persona_memories
        )

        # 7. 生成回复
        response = self.llm.generate_response(
            message,
            system_prompt,
            conversation_history
        )

        # 8. 分段处理回复
        response_segments = self.message_processor.process_message(response)

        # 9. 保存对话记录
        self.db.save_conversation(user_id, message, 'user', emotion_analysis.get('intensity', 0.0))
        self.db.save_conversation(user_id, response, 'assistant')

        # 10. 提取并保存记忆信息
        memories = self.memory_manager.extract_memories_from_text(user_id, message)

        return {
            'response': response,
            'response_segments': response_segments,
            'affection_level': current_affection,
            'affection_change': affection_change,
            'emotion': emotion_analysis.get('emotion', 'neutral'),
            'memories_extracted': len(memories),
            'persona_activity': self.persona.get_current_activity()
        }

    def _calculate_affection_change(self, message: str, emotion_analysis: Dict) -> int:
        """计算好感度变化"""
        change = 0
        message_lower = message.lower()

        # 基于消息内容的好感度变化
        if any(word in message_lower for word in ['谢谢', '感谢', '喜欢', '开心']):
            change += Config.AFFECTION_CONFIG['interaction_bonus']['positive_chat']

        if any(word in message_lower for word in ['分享', '告诉你', '我觉得', '我想']):
            change += Config.AFFECTION_CONFIG['interaction_bonus']['share_personal']

        if any(word in message_lower for word in ['你怎么样', '你好吗', '你在做什么', '你的工作']):
            change += Config.AFFECTION_CONFIG['interaction_bonus']['ask_about_her']

        if any(word in message_lower for word in ['早上好', '早安', '晚安', '晚上好']):
            change += Config.AFFECTION_CONFIG['interaction_bonus']['good_morning']

        # 基于情感的调整
        emotion = emotion_analysis.get('emotion', 'neutral')
        intensity = emotion_analysis.get('intensity', 0.5)

        if emotion == 'happy' and intensity > 0.7:
            change += 1
        elif emotion in ['angry', 'sad'] and intensity > 0.8:
            change -= 1

        return change

    def _build_system_prompt(
        self,
        affection_level: int,
        emotion_analysis: Dict,
        memories: List[Dict],
        persona_memories: List[Dict] = None
    ) -> str:
        """构建系统提示词 - 使用提示词模块"""

        # 获取当前活动状态
        current_activity = self.persona.get_current_activity()

        # 获取人设信息
        persona_info = self.persona.get_persona_info()

        # 准备模板参数
        template_params = {
            # 人设信息
            'persona': {
                'name': persona_info.get('name', '小雨'),
                'age': persona_info.get('age', 25),
                'profession': persona_info.get('profession', '心理咨询师'),
                'personality': persona_info.get('personality', '温和、善解人意'),
                'hobbies': persona_info.get('hobbies', []),  # 传递完整的爱好列表
                'speaking_style': persona_info.get('speaking_style', '温暖亲切'),
                'other_infos': persona_info.get('other_infos', []),  # 传递其他信息
            },

            # 当前状态
            'current_time': datetime.now(),
            'current_activity': current_activity['activity'],
            'activity_description': current_activity.get('description', ''),

            # 时间上下文
            'time_context': {
                'period': current_activity['time_period'],
                'greeting': current_activity.get('greeting', ''),
                'mood': current_activity['mood']
            },

            # 记忆信息
            'memories': memories,
            'persona_memories': persona_memories,

            # 情感分析
            'emotion_analysis': emotion_analysis,

            # 好感度
            'affection_level': affection_level,

            # 说话示例
            'speaking_examples': Config.PERSONA_CONFIG.get('speaking_examples', [])
        }

        # 使用提示词模块渲染系统提示词
        return self.prompt_manager.render_system_prompt(**template_params)

    def get_greeting_message(self, user_id: str) -> Dict:
        """获取问候消息"""
        user = self.db.get_user_or_create(user_id)
        current_affection = self.db.get_current_affection(user_id)

        # 获取时间相关的问候
        greeting = self.persona.get_time_based_greeting()

        # 根据好感度调整问候
        if current_affection > 60:
            greeting = greeting.replace("你", "你")  # 可以添加更亲密的称呼

        return {
            'response': greeting,
            'affection_level': current_affection,
            'persona_activity': self.persona.get_current_activity()
        }
