#!/usr/bin/env python3
"""
测试LLM智能分段功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.conversation_engine import ConversationEngine
import time

def test_llm_segmentation_in_conversation():
    """测试在实际对话中的LLM分段效果"""
    print("=== 测试LLM智能分段在对话中的效果 ===\n")
    
    engine = ConversationEngine()
    user_id = "test_llm_segmentation_user"
    
    # 模拟对话
    messages = [
        "你好！今天过得怎么样？",
        "我今天去了一家很棒的咖啡店，环境特别好，还买了一本心理学的书。",
        "你最近在忙什么呢？有什么有趣的事情吗？",
    ]
    
    print("开始模拟对话，观察LLM分段效果...")
    
    for i, message in enumerate(messages, 1):
        print(f"\n--- 第 {i} 轮对话 ---")
        print(f"👤 用户: {message}")
        
        # 处理消息
        result = engine.process_message(user_id, message)
        
        # 显示分段结果
        segments = result.get('response_segments', [result['response']])
        
        print(f"🤖 小雨回复 ({len(segments)} 段):")
        for j, segment in enumerate(segments, 1):
            print(f"  {j}. {segment}")
            time.sleep(0.5)  # 模拟发送间隔
        
        print(f"📊 好感度: {result['affection_level']} (变化: {result['affection_change']:+d})")
        print(f"😊 情感: {result['emotion']}")
        print(f"🎭 活动: {result['persona_activity']['activity'][:30]}...")
        
        time.sleep(1)  # 对话间隔

def test_segmentation_comparison():
    """对比LLM分段和备用分段的效果"""
    print("\n=== LLM分段 vs 备用分段对比 ===\n")
    
    engine = ConversationEngine()
    
    test_texts = [
        "我刚才在画画，画了一朵很漂亮的花。不过现在有点累了，想休息一下。对了，你今天过得怎么样？",
        "哈哈，你真有趣！我也很喜欢和你聊天。咱们聊点别的吧，你最近有什么新发现吗？",
        "今天天气真不错，阳光很温暖。我想出去走走，你要不要一起？",
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"测试文本 {i}: {text}")
        
        # LLM分段
        llm_segments = engine._llm_split_message(text)
        print(f"🤖 LLM分段 ({len(llm_segments)} 段):")
        for j, seg in enumerate(llm_segments, 1):
            print(f"  {j}. {seg}")
        
        # 备用分段
        fallback_segments = engine._fallback_split(text)
        print(f"⚙️  备用分段 ({len(fallback_segments)} 段):")
        for j, seg in enumerate(fallback_segments, 1):
            print(f"  {j}. {seg}")
        
        print("-" * 60)

def test_segmentation_edge_cases():
    """测试分段的边界情况"""
    print("\n=== 分段边界情况测试 ===\n")
    
    engine = ConversationEngine()
    
    edge_cases = [
        "好的",  # 极短文本
        "嗯嗯，知道了，谢谢你！",  # 短文本
        "我今天真的很开心，因为遇到了很多有趣的事情，比如看到了一只很可爱的小猫，还在咖啡店里读了一本很棒的书，感觉整个人都充满了正能量，你今天过得怎么样呢？",  # 长文本
        "哈哈哈哈哈哈哈哈哈哈",  # 重复文本
        "emmm...这个问题有点复杂呢，我需要想想。",  # 包含省略号
    ]
    
    for i, text in enumerate(edge_cases, 1):
        print(f"边界情况 {i}: {text}")
        
        segments = engine._split_response_into_segments(text)
        print(f"分段结果 ({len(segments)} 段):")
        for j, seg in enumerate(segments, 1):
            print(f"  {j}. {seg}")
        
        print("-" * 40)

if __name__ == "__main__":
    print("🧪 LLM智能分段功能测试")
    print("=" * 60)
    
    # 测试1: 实际对话中的分段效果
    test_llm_segmentation_in_conversation()
    
    # 测试2: 分段方法对比
    test_segmentation_comparison()
    
    # 测试3: 边界情况测试
    test_segmentation_edge_cases()
    
    print("\n" + "=" * 60)
    print("🎉 LLM智能分段测试完成！")
    print("✨ 新的分段方式更自然，有长有短，更贴近真实聊天习惯！")
