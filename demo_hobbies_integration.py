#!/usr/bin/env python3
"""
演示爱好信息集成到提示词的效果
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.conversation_engine import ConversationEngine
from services.prompt_manager import PromptManager

def demo_hobbies_integration():
    """演示爱好信息集成"""
    print("🎨 爱好信息集成演示")
    print("=" * 60)
    
    # 创建对话引擎
    engine = ConversationEngine()
    
    # 模拟一个对话场景
    print("\n📝 模拟对话场景:")
    print("用户: 我最近也在学画画，你有什么建议吗？")
    
    # 构建系统提示词
    test_memories = [
        {'content': '用户喜欢画画'},
        {'content': '用户最近在学习新技能'}
    ]
    
    test_persona_memories = [
        {
            'title': '第一次画展',
            'content': '我记得第一次参加画展时很紧张，但是很有成就感',
            'emotion': 'nervous'
        }
    ]
    
    test_emotion_analysis = {
        'emotion': 'curious',
        'intensity': 0.7,
        'keywords': ['学习', '建议']
    }
    
    system_prompt = engine._build_system_prompt(
        affection_level=65,
        emotion_analysis=test_emotion_analysis,
        memories=test_memories,
        persona_memories=test_persona_memories
    )
    
    print(f"\n📏 生成的系统提示词长度: {len(system_prompt)} 字符")
    
    # 分析提示词中的爱好信息
    print("\n🔍 提示词中包含的爱好信息分析:")
    print("-" * 40)
    
    hobby_keywords = [
        ('画画', '绘画艺术'),
        ('周杰伦', '音乐偏好'),
        ('演唱会', '音乐活动经历'),
        ('旅游', '旅行经历'),
        ('摄影', '摄影爱好'),
        ('火锅', '美食偏好'),
        ('奶茶', '饮品偏好'),
        ('韩团', 'K-pop文化'),
        ('电影', '影视偏好'),
        ('滑雪', '运动爱好'),
        ('富士相机', '摄影设备'),
        ('泰山', '登山经历'),
        ('首尔', '国际旅行'),
        ('传媒大学', '教育背景'),
        ('射手座', '星座信息'),
        ('ENFJ', '性格类型')
    ]
    
    found_hobbies = []
    for keyword, category in hobby_keywords:
        if keyword in system_prompt:
            found_hobbies.append((keyword, category))
            print(f"   ✅ {category}: {keyword}")
    
    print(f"\n📊 统计: 共找到 {len(found_hobbies)} 个爱好相关信息")
    
    # 展示人设上下文部分
    print("\n📋 人设上下文部分预览:")
    print("-" * 40)
    
    # 提取人设部分（从"你的基本信息"开始到"当前状态"结束）
    start_marker = "你的基本信息："
    end_marker = "当前状态："
    
    start_idx = system_prompt.find(start_marker)
    end_idx = system_prompt.find(end_marker)
    
    if start_idx != -1 and end_idx != -1:
        persona_section = system_prompt[start_idx:end_idx].strip()
        lines = persona_section.split('\n')
        
        # 显示前20行
        for i, line in enumerate(lines[:20]):
            print(f"   {i+1:2d}: {line}")
        
        if len(lines) > 20:
            print(f"   ... (还有 {len(lines)-20} 行)")
    
    # 展示爱好信息的详细程度
    print(f"\n🎯 爱好信息详细程度分析:")
    print("-" * 40)
    
    hobby_categories = {
        '音乐': ['周杰伦', '林俊杰', '邓紫棋', '演唱会', 'kpop', 'aespa'],
        '旅游': ['云南', '青岛', '厦门', '首尔', '泰山', '华山'],
        '摄影': ['富士相机', 'XT30', 'X100V', '佳能', '风景照', '旅拍'],
        '美食': ['火锅', '烤肉', '奶茶', '霸王茶姬', '茶颜悦色'],
        '艺术': ['画画', '《中国十大传世名画》', '美术学院', '原画师'],
        '运动': ['滑雪', '游泳', '爬山'],
        '娱乐': ['电影', '漫画', '小说', '追星']
    }
    
    for category, keywords in hobby_categories.items():
        found_in_category = [kw for kw in keywords if kw in system_prompt]
        coverage = len(found_in_category) / len(keywords) * 100
        print(f"   {category}: {len(found_in_category)}/{len(keywords)} 项 ({coverage:.1f}%)")
        if found_in_category:
            print(f"      包含: {', '.join(found_in_category[:3])}{'...' if len(found_in_category) > 3 else ''}")

def demo_template_customization():
    """演示模板自定义"""
    print("\n\n🛠️  模板自定义演示")
    print("=" * 60)
    
    prompt_manager = PromptManager()
    
    print("📁 当前模板文件:")
    templates_dir = os.path.join(os.path.dirname(__file__), 'backend', 'templates', 'prompts')
    
    if os.path.exists(templates_dir):
        files = [f for f in os.listdir(templates_dir) if f.endswith('.j2')]
        for i, filename in enumerate(sorted(files), 1):
            filepath = os.path.join(templates_dir, filename)
            size = os.path.getsize(filepath)
            print(f"   {i:2d}. {filename:<25} ({size:4d} bytes)")
    
    print(f"\n💡 自定义建议:")
    print("1. 编辑 persona_context.j2 来调整人设信息的展示方式")
    print("2. 修改 jailbreak_prompt.j2 来调整角色扮演的基调")
    print("3. 调整 affection_context.j2 来优化好感度相关的行为")
    print("4. 自定义 examples_context.j2 来添加更多说话示例")
    
    print(f"\n📂 模板文件位置: {templates_dir}")
    print("📝 修改模板文件后会自动生效，无需重启服务")

if __name__ == "__main__":
    print("🎨 爱好信息集成到提示词演示")
    print("=" * 60)
    
    demo_hobbies_integration()
    demo_template_customization()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n✨ 主要成果:")
    print("1. ✅ 爱好信息成功集成到提示词模板中")
    print("2. ✅ 支持详细的个人信息渲染（音乐、旅游、摄影、美食等）")
    print("3. ✅ 模板化管理，便于自定义和维护")
    print("4. ✅ 动态参数注入，支持个性化定制")
    print("5. ✅ 完整的人设信息展示，增强对话真实感")
    
    print(f"\n🔧 技术特点:")
    print("- 使用 Jinja2 模板引擎进行动态渲染")
    print("- 支持条件渲染和循环遍历")
    print("- 模板文件独立管理，便于版本控制")
    print("- 热更新支持，修改即生效")
    print("- 结构化数据传递，类型安全")
