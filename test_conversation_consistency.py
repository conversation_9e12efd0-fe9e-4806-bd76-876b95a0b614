#!/usr/bin/env python3
"""
测试对话中活动状态的一致性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.conversation_engine import ConversationEngine
import time

def test_conversation_activity_consistency():
    """测试在对话过程中活动状态的一致性"""
    print("=== 测试对话中活动状态一致性 ===\n")
    
    engine = ConversationEngine()
    user_id = "test_consistency_user"
    
    # 模拟多轮对话
    messages = [
        "你好！",
        "今天天气怎么样？",
        "你在做什么呢？",
        "心情如何？",
        "有什么想聊的吗？"
    ]
    
    activities = []
    
    print("开始模拟对话...")
    for i, message in enumerate(messages, 1):
        print(f"\n--- 第 {i} 轮对话 ---")
        print(f"用户: {message}")
        
        # 获取问候消息（第一轮）或处理消息
        if i == 1:
            result = engine.get_greeting_message(user_id)
        else:
            result = engine.process_message(user_id, message)
        
        activity = result['persona_activity']
        activities.append(activity)
        
        print(f"小雨: {result['response'][:50]}...")
        print(f"活动状态: {activity['activity']}")
        print(f"心情: {activity['mood']}")
        print(f"时间段: {activity['time_period']}")
        
        # 短暂延迟模拟真实对话
        time.sleep(0.5)
    
    # 检查活动状态一致性
    print("\n" + "=" * 60)
    print("活动状态一致性检查:")
    
    first_activity = activities[0]
    consistent = True
    
    for i, activity in enumerate(activities, 1):
        is_same = (activity['activity'] == first_activity['activity'] and 
                  activity['mood'] == first_activity['mood'])
        
        status = "✅" if is_same else "❌"
        print(f"  第{i}轮: {status} {activity['activity'][:30]}... | {activity['mood']}")
        
        if not is_same:
            consistent = False
    
    print("\n" + "=" * 60)
    if consistent:
        print("🎉 测试通过！所有对话轮次中活动状态保持一致")
    else:
        print("⚠️  测试失败！活动状态在对话中发生了变化")
    
    return consistent

def test_greeting_vs_chat_consistency():
    """测试问候消息和聊天消息中活动状态的一致性"""
    print("\n=== 测试问候与聊天活动状态一致性 ===\n")
    
    engine = ConversationEngine()
    user_id = "test_greeting_user"
    
    # 获取问候消息
    greeting_result = engine.get_greeting_message(user_id)
    greeting_activity = greeting_result['persona_activity']
    
    print("问候消息:")
    print(f"  回复: {greeting_result['response']}")
    print(f"  活动: {greeting_activity['activity']}")
    print(f"  心情: {greeting_activity['mood']}")
    
    # 发送聊天消息
    chat_result = engine.process_message(user_id, "你好呀！")
    chat_activity = chat_result['persona_activity']
    
    print("\n聊天消息:")
    print(f"  回复: {chat_result['response'][:50]}...")
    print(f"  活动: {chat_activity['activity']}")
    print(f"  心情: {chat_activity['mood']}")
    
    # 检查一致性
    is_consistent = (greeting_activity['activity'] == chat_activity['activity'] and
                    greeting_activity['mood'] == chat_activity['mood'])
    
    print("\n" + "-" * 50)
    if is_consistent:
        print("✅ 问候和聊天中的活动状态一致")
    else:
        print("❌ 问候和聊天中的活动状态不一致")
        print(f"   问候活动: {greeting_activity['activity']}")
        print(f"   聊天活动: {chat_activity['activity']}")
    
    return is_consistent

if __name__ == "__main__":
    print("🧪 对话活动状态一致性测试")
    print("=" * 60)
    
    test1_passed = test_conversation_activity_consistency()
    test2_passed = test_greeting_vs_chat_consistency()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"  对话一致性测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  问候聊天一致性测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！活动状态一致性修正成功！")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试。")
