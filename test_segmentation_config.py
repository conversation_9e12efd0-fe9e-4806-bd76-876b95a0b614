#!/usr/bin/env python3
"""
测试消息分段配置功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_fallback_segmentation():
    """测试备用分段功能（默认配置）"""
    print("=== 测试备用分段功能 ===\n")
    
    from services.conversation_engine import ConversationEngine
    from config import Config
    
    # 显示当前配置
    print(f"当前配置:")
    print(f"  USE_LLM_SPLIT: {Config.MESSAGE_SEGMENTATION_CONFIG['use_llm_split']}")
    print(f"  fallback_max_length: {Config.MESSAGE_SEGMENTATION_CONFIG['fallback_max_length']}")
    print()
    
    engine = ConversationEngine()
    
    test_cases = [
        "你好！今天天气真不错。你在做什么呢？",
        "我刚才在画画，画了一朵很漂亮的花。不过现在有点累了，想休息一下。对了，你今天过得怎么样？",
        "哈哈，你真有趣！我也很喜欢和你聊天。",
        "今天我去了一家新开的咖啡店，环境很棒，咖啡也很香。然后我还买了一本书，是关于心理学的。你有什么推荐的书吗？",
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"测试用例 {i}:")
        print(f"原始消息: {text}")
        
        segments = engine._split_response_into_segments(text)
        
        print(f"分段结果 ({len(segments)} 段):")
        for j, segment in enumerate(segments, 1):
            print(f"  {j}. {segment}")
        print("-" * 50)

def test_llm_segmentation():
    """测试LLM分段功能（需要手动设置环境变量）"""
    print("\n=== 测试LLM分段功能 ===\n")
    
    # 临时设置环境变量
    os.environ['USE_LLM_SPLIT'] = 'true'
    
    # 重新导入配置以获取新的环境变量值
    import importlib
    import config
    importlib.reload(config)
    
    from services.conversation_engine import ConversationEngine
    from config import Config
    
    # 显示当前配置
    print(f"当前配置:")
    print(f"  USE_LLM_SPLIT: {Config.MESSAGE_SEGMENTATION_CONFIG['use_llm_split']}")
    print(f"  fallback_max_length: {Config.MESSAGE_SEGMENTATION_CONFIG['fallback_max_length']}")
    print()
    
    engine = ConversationEngine()
    
    test_cases = [
        "我刚才在画画，画了一朵很漂亮的花。不过现在有点累了，想休息一下。对了，你今天过得怎么样？",
        "今天我去了一家新开的咖啡店，环境很棒，咖啡也很香。然后我还买了一本书，是关于心理学的。你有什么推荐的书吗？",
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"测试用例 {i}:")
        print(f"原始消息: {text}")
        
        segments = engine._split_response_into_segments(text)
        
        print(f"分段结果 ({len(segments)} 段):")
        for j, segment in enumerate(segments, 1):
            print(f"  {j}. {segment}")
        print("-" * 50)
    
    # 恢复环境变量
    os.environ['USE_LLM_SPLIT'] = 'false'

def test_configuration_comparison():
    """对比两种配置的分段效果"""
    print("\n=== 配置对比测试 ===\n")
    
    test_text = "我刚才在画画，画了一朵很漂亮的花。不过现在有点累了，想休息一下。对了，你今天过得怎么样？"
    
    print(f"测试文本: {test_text}\n")
    
    # 测试备用分段
    print("🔧 备用分段结果:")
    os.environ['USE_LLM_SPLIT'] = 'false'
    import importlib
    import config
    importlib.reload(config)
    
    from services.conversation_engine import ConversationEngine
    engine = ConversationEngine()
    
    fallback_segments = engine._split_response_into_segments(test_text)
    for i, seg in enumerate(fallback_segments, 1):
        print(f"  {i}. {seg}")
    
    print(f"\n备用分段: {len(fallback_segments)} 段")
    
    # 测试LLM分段
    print("\n🤖 LLM分段结果:")
    os.environ['USE_LLM_SPLIT'] = 'true'
    importlib.reload(config)
    
    engine = ConversationEngine()
    llm_segments = engine._split_response_into_segments(test_text)
    for i, seg in enumerate(llm_segments, 1):
        print(f"  {i}. {seg}")
    
    print(f"\nLLM分段: {len(llm_segments)} 段")
    
    # 恢复默认设置
    os.environ['USE_LLM_SPLIT'] = 'false'
    
    print("\n📊 对比总结:")
    print(f"  备用分段: {len(fallback_segments)} 段，节约token")
    print(f"  LLM分段: {len(llm_segments)} 段，更自然但消耗token")

if __name__ == "__main__":
    print("🧪 消息分段配置测试")
    print("=" * 60)
    
    # 测试1: 备用分段（默认）
    test_fallback_segmentation()
    
    # 测试2: LLM分段
    test_llm_segmentation()
    
    # 测试3: 配置对比
    test_configuration_comparison()
    
    print("\n" + "=" * 60)
    print("🎉 配置测试完成！")
    print("💡 提示: 在 .env 文件中设置 USE_LLM_SPLIT=true 可启用LLM分段")
    print("💰 建议: 生产环境可设置为 false 以节约token成本")
